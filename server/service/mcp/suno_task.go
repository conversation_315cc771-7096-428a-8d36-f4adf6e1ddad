package mcp

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	mcpReq "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type SunoTaskService struct{}

// CreateSunoTask 创建Suno任务
func (s *SunoTaskService) CreateSunoTask(req mcpReq.CreateSunoTaskReq) (sunoTask mcp.SunoTask, err error) {
	var existTask mcp.SunoTask
	if !errors.Is(global.GVA_DB.Where("task_id = ?", req.TaskID).First(&existTask).Error, gorm.ErrRecordNotFound) {
		return sunoTask, errors.New("任务ID已存在")
	}

	sunoTask = mcp.SunoTask{
		TaskID:       req.TaskID,
		UserUUID:     req.UserUUID,
		CallbackType: req.CallbackType,
		Prompt:       req.Prompt,
		ModelName:    req.ModelName,
		Status:       mcp.TaskStatusPending,
	}

	return sunoTask, global.GVA_DB.Create(&sunoTask).Error
}

// ProcessCallback 处理Suno回调
func (s *SunoTaskService) ProcessCallback(callbackReq mcp.SunoCallbackRequest) error {
	global.GVA_LOG.Info("开始处理Suno回调",
		zap.String("taskId", callbackReq.Data.TaskID),
		zap.String("callbackType", callbackReq.Data.CallbackType),
		zap.Int("code", callbackReq.Code),
		zap.String("msg", callbackReq.Msg))

	if callbackReq.Code != 200 {
		// 处理失败回调
		global.GVA_LOG.Warn("收到失败回调", zap.String("taskId", callbackReq.Data.TaskID), zap.String("error", callbackReq.Msg))
		return s.updateTaskStatus(callbackReq.Data.TaskID, mcp.TaskStatusFailed, callbackReq.Msg)
	}

	// 根据回调类型处理
	switch callbackReq.Data.CallbackType {
	case mcp.CallbackTypeComplete, mcp.CallbackTypeFirst:
		// 处理音乐生成完成回调
		global.GVA_LOG.Info("处理音乐生成完成回调", zap.String("taskId", callbackReq.Data.TaskID))
		for _, musicData := range callbackReq.Data.Data {
			err := s.saveOrUpdateTask(callbackReq.Data.TaskID, callbackReq.Data.CallbackType, musicData)
			if err != nil {
				global.GVA_LOG.Error("保存任务数据失败", zap.Error(err), zap.String("taskId", callbackReq.Data.TaskID))
				return err
			}
		}
	case mcp.CallbackTypeText:
		// 处理文本生成完成回调
		global.GVA_LOG.Info("处理文本生成完成回调", zap.String("taskId", callbackReq.Data.TaskID))
		// 这里可以保存文本生成结果，暂时只记录日志
	case mcp.CallbackTypeError:
		// 处理错误回调
		global.GVA_LOG.Error("收到错误回调", zap.String("taskId", callbackReq.Data.TaskID), zap.String("error", callbackReq.Msg))
		return s.updateTaskStatus(callbackReq.Data.TaskID, mcp.TaskStatusFailed, callbackReq.Msg)
	default:
		// 处理其他类型的回调（兼容旧版本）
		global.GVA_LOG.Info("处理通用回调", zap.String("taskId", callbackReq.Data.TaskID), zap.String("callbackType", callbackReq.Data.CallbackType))
		for _, musicData := range callbackReq.Data.Data {
			err := s.saveOrUpdateTask(callbackReq.Data.TaskID, callbackReq.Data.CallbackType, musicData)
			if err != nil {
				global.GVA_LOG.Error("保存任务数据失败", zap.Error(err), zap.String("taskId", callbackReq.Data.TaskID))
				return err
			}
		}
	}

	global.GVA_LOG.Info("Suno回调处理完成", zap.String("taskId", callbackReq.Data.TaskID))
	return nil
}

// saveOrUpdateTask 保存或更新任务数据
func (s *SunoTaskService) saveOrUpdateTask(taskID, callbackType string, musicData mcp.SunoMusic) error {
	global.GVA_LOG.Info("开始保存或更新任务数据",
		zap.String("taskId", taskID),
		zap.String("callbackType", callbackType),
		zap.String("musicId", musicData.ID),
		zap.String("title", musicData.Title),
		zap.String("audioUrl", musicData.AudioURL))

	var sunoTask mcp.SunoTask

	// 解析创建时间
	var taskCreateTime *time.Time
	if musicData.CreateTime != nil {
		switch v := musicData.CreateTime.(type) {
		case string:
			if v != "" {
				if parsedTime, err := time.Parse("2006-01-02T15:04:05.000Z", v); err == nil {
					taskCreateTime = &parsedTime
				} else if parsedTime, err := time.Parse("2006-01-02 15:04:05", v); err == nil {
					taskCreateTime = &parsedTime
				} else {
					global.GVA_LOG.Warn("无法解析字符串格式的创建时间", zap.String("createTime", v))
				}
			}
		case float64:
			// 处理时间戳
			if v > 0 {
				var parsedTime time.Time
				if v > 1e10 {
					// 毫秒级时间戳
					parsedTime = time.UnixMilli(int64(v))
				} else {
					// 秒级时间戳
					parsedTime = time.Unix(int64(v), 0)
				}
				taskCreateTime = &parsedTime
				global.GVA_LOG.Info("解析时间戳成功", zap.Float64("timestamp", v), zap.Time("parsedTime", parsedTime))
			}
		case int64:
			// 处理时间戳
			if v > 0 {
				var parsedTime time.Time
				if v > 1e10 {
					// 毫秒级时间戳
					parsedTime = time.UnixMilli(v)
				} else {
					// 秒级时间戳
					parsedTime = time.Unix(v, 0)
				}
				taskCreateTime = &parsedTime
				global.GVA_LOG.Info("解析时间戳成功", zap.Int64("timestamp", v), zap.Time("parsedTime", parsedTime))
			}
		default:
			global.GVA_LOG.Warn("不支持的创建时间格式", zap.Any("createTime", musicData.CreateTime), zap.String("type", fmt.Sprintf("%T", musicData.CreateTime)))
		}
	}

	// 查找现有任务
	err := global.GVA_DB.Where("task_id = ?", taskID).First(&sunoTask).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 创建新任务
		global.GVA_LOG.Info("创建新任务记录", zap.String("taskId", taskID))
		sunoTask = mcp.SunoTask{
			TaskID:               taskID,
			MusicID:              musicData.ID,
			AudioURL:             musicData.AudioURL,
			SourceAudioURL:       musicData.SourceAudioURL,
			StreamAudioURL:       musicData.StreamAudioURL,
			SourceStreamAudioURL: musicData.SourceStreamAudioURL,
			ImageURL:             musicData.ImageURL,
			SourceImageURL:       musicData.SourceImageURL,
			Prompt:               musicData.Prompt,
			ModelName:            musicData.ModelName,
			Title:                musicData.Title,
			Tags:                 musicData.Tags,
			Duration:             musicData.Duration,
			CallbackType:         callbackType,
			Status:               mcp.TaskStatusCompleted,
			TaskCreateTime:       taskCreateTime,
		}

		createErr := global.GVA_DB.Create(&sunoTask).Error
		if createErr != nil {
			global.GVA_LOG.Error("创建任务记录失败", zap.Error(createErr), zap.String("taskId", taskID))
			return createErr
		}
		global.GVA_LOG.Info("任务记录创建成功", zap.String("taskId", taskID), zap.Uint("recordId", sunoTask.ID))
		return nil
	} else if err != nil {
		global.GVA_LOG.Error("查询现有任务失败", zap.Error(err), zap.String("taskId", taskID))
		return err
	}

	// 更新现有任务
	global.GVA_LOG.Info("更新现有任务记录", zap.String("taskId", taskID), zap.Uint("recordId", sunoTask.ID))
	updates := map[string]interface{}{
		"music_id":                musicData.ID,
		"audio_url":               musicData.AudioURL,
		"source_audio_url":        musicData.SourceAudioURL,
		"stream_audio_url":        musicData.StreamAudioURL,
		"source_stream_audio_url": musicData.SourceStreamAudioURL,
		"image_url":               musicData.ImageURL,
		"source_image_url":        musicData.SourceImageURL,
		"prompt":                  musicData.Prompt,
		"model_name":              musicData.ModelName,
		"title":                   musicData.Title,
		"tags":                    musicData.Tags,
		"duration":                musicData.Duration,
		"callback_type":           callbackType,
		"status":                  mcp.TaskStatusCompleted,
		"error_message":           "", // 清除错误信息
	}

	if taskCreateTime != nil {
		updates["task_create_time"] = taskCreateTime
	}

	updateErr := global.GVA_DB.Model(&sunoTask).Updates(updates).Error
	if updateErr != nil {
		global.GVA_LOG.Error("更新任务记录失败", zap.Error(updateErr), zap.String("taskId", taskID))
		return updateErr
	}

	global.GVA_LOG.Info("任务记录更新成功", zap.String("taskId", taskID))
	return nil
}

// updateTaskStatus 更新任务状态
func (s *SunoTaskService) updateTaskStatus(taskID, status, errorMsg string) error {
	updates := map[string]interface{}{
		"status": status,
	}
	if errorMsg != "" {
		updates["error_message"] = errorMsg
	}

	return global.GVA_DB.Model(&mcp.SunoTask{}).Where("task_id = ?", taskID).Updates(updates).Error
}

// GetSunoTaskByID 根据任务ID获取任务
func (s *SunoTaskService) GetSunoTaskByID(taskID string) (sunoTask mcp.SunoTask, err error) {
	err = global.GVA_DB.Where("task_id = ?", taskID).First(&sunoTask).Error
	return
}

// GetSunoTaskList 获取任务列表
func (s *SunoTaskService) GetSunoTaskList(info mcpReq.SunoTaskSearchReq) (list interface{}, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := global.GVA_DB.Model(&mcp.SunoTask{})

	var sunoTasks []mcp.SunoTask

	// 构建查询条件
	if info.TaskID != "" {
		db = db.Where("task_id LIKE ?", "%"+info.TaskID+"%")
	}
	if info.Status != "" {
		db = db.Where("status = ?", info.Status)
	}
	if info.CallbackType != "" {
		db = db.Where("callback_type = ?", info.CallbackType)
	}
	if info.UserUUID != "" {
		db = db.Where("user_uuid = ?", info.UserUUID)
	}
	if info.StartTime != "" {
		db = db.Where("created_at >= ?", info.StartTime)
	}
	if info.EndTime != "" {
		db = db.Where("created_at <= ?", info.EndTime)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&sunoTasks).Error
	return sunoTasks, total, err
}

// CallSunoAPI 调用Suno API查询任务进度
func (s *SunoTaskService) CallSunoAPI(taskID string) (interface{}, error) {
	// 检查配置
	if global.GVA_CONFIG.Suno.BaseURL == "" {
		return nil, errors.New("Suno API base URL 未配置")
	}

	// 构建API URL
	apiURL := fmt.Sprintf("%s/api/v1/task/%s", global.GVA_CONFIG.Suno.BaseURL, taskID)

	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	if global.GVA_CONFIG.Suno.APIKey != "" {
		req.Header.Set("Authorization", "Bearer "+global.GVA_CONFIG.Suno.APIKey)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode == 404 {
		return nil, errors.New("任务进行中，请稍后再查询...")
	}
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("Suno API返回错误状态码: %d", resp.StatusCode)
	}

	var result map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&result)
	if err != nil {
		return nil, err
	}

	// 检查API响应中的错误状态
	if result["code"] != nil {
		if code, ok := result["code"].(float64); ok && code != 200 {
			return nil, fmt.Errorf("Suno API返回错误: %v", result["msg"])
		}
	}

	return result, nil
}

// QueryTaskProgress 查询任务进度（先查数据库，再查API）
func (s *SunoTaskService) QueryTaskProgress(taskID string) (interface{}, error) {
	// 先查数据库
	sunoTask, err := s.GetSunoTaskByID(taskID)
	if err == nil {
		// 如果任务已完成，直接返回数据库结果
		if sunoTask.Status == mcp.TaskStatusCompleted {
			return sunoTask, nil
		}
		// 如果任务还在进行中，调用API获取最新状态
		if sunoTask.Status == mcp.TaskStatusPending || sunoTask.Status == mcp.TaskStatusRunning {
			apiResult, apiErr := s.CallSunoAPI(taskID)
			if apiErr == nil {
				return apiResult, nil
			}
			// API调用失败，返回数据库中的数据
			global.GVA_LOG.Warn("调用Suno API失败，返回数据库数据", zap.Error(apiErr), zap.String("taskId", taskID))
			return sunoTask, nil
		}
		// 如果任务失败，返回数据库中的失败信息
		if sunoTask.Status == mcp.TaskStatusFailed {
			return sunoTask, nil
		}
	}

	// 数据库中没有记录，直接调用Suno API查询
	if errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Info("数据库中未找到任务，尝试从Suno API查询", zap.String("taskId", taskID))
		apiResult, apiErr := s.CallSunoAPI(taskID)
		if apiErr != nil {
			// 如果API也失败，返回pending状态而不是错误
			global.GVA_LOG.Warn("Suno API查询失败，返回pending状态", zap.Error(apiErr), zap.String("taskId", taskID))
			return map[string]interface{}{
				"taskId":   taskID,
				"status":   "pending",
				"progress": 0,
				"message":  "任务正在处理中，请稍后再查询...",
				"result":   nil,
			}, nil
		}
		return apiResult, nil
	}

	return nil, err
}
