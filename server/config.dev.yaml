aliyun-oss:
  endpoint: oss-cn-shanghai.aliyuncs.com
  access-key-id: LTAI5tH41DnuiZdFEwgexy2C
  access-key-secret: ******************************
  bucket-name: jue<PERSON>
  bucket-url: https://juezhi.oss-cn-shanghai.aliyuncs.com
  base-path: file
authing:
  app-id: 68146b9b38c2bc6f359d189e
  secret: 1ff72dbb1177aa1ff752baedc2adbefd
  user-pool-id: 67f27217ec5db525e6db9f55
  user-pool-secret: 2fb33ec419423d55009db3c2644b7acf
  host: https://mcpcn.authing.cn
  redirect-uri: https://rapido.chat/api/auth/handleAuthingCallback
  token-endpoint: https://mcpcn.authing.cn/oidc/token
autocode:
  web: web/src
  root: /Users/<USER>/cursor/mcp-server
  server: server
  module: github.com/flipped-aurora/gin-vue-admin/server
  ai-path: ""
aws-s3:
  bucket: xxxxx-********
  region: ap-shanghai
  endpoint: ""
  secret-id: your-secret-id
  secret-key: your-secret-key
  base-url: https://gin.vue.admin
  path-prefix: github.com/flipped-aurora/gin-vue-admin/server
  s3-force-path-style: false
  disable-ssl: false
captcha:
  key-long: 6
  img-width: 240
  img-height: 80
  open-captcha: 0
  open-captcha-timeout: 3600
cloudflare-r2:
  bucket: xxxx0bucket
  base-url: https://gin.vue.admin.com
  path: uploads
  account-id: xxx_account_id
  access-key-id: xxx_key_id
  secret-access-key: xxx_secret_key
cors:
  mode: whitelist
  whitelist:
    - allow-origin: http://localhost:8888
      allow-methods: GET,POST,PUT,DELETE,OPTIONS
      allow-headers: Content-Type,AccessToken,X-CSRF-Token,Authorization,Token,X-Token,X-User-Id
      expose-headers: Content-Length,Access-Control-Allow-Origin,Access-Control-Allow-Headers,Content-Type
      allow-credentials: true
    - allow-origin: http://127.0.0.1:8888
      allow-methods: GET,POST,PUT,DELETE,OPTIONS
      allow-headers: Content-Type,AccessToken,X-CSRF-Token,Authorization,Token,X-Token,X-User-Id
      expose-headers: Content-Length,Access-Control-Allow-Origin,Access-Control-Allow-Headers,Content-Type
      allow-credentials: true
    - allow-origin: https://location.mcpcn.ai
      allow-methods: GET,POST,PUT,DELETE,OPTIONS
      allow-headers: Content-Type,AccessToken,X-CSRF-Token,Authorization,Token,X-Token,X-User-Id
      expose-headers: Content-Length,Access-Control-Allow-Origin,Access-Control-Allow-Headers,Content-Type
      allow-credentials: true
db-list:
  - type: ""
    alias-name: ""
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
    disable: true
disk-list:
  - mount-point: /
email:
  to: <EMAIL>
  from: <EMAIL>
  host: smtp.163.com
  secret: xxx
  nickname: test
  port: 465
  is-ssl: true
excel:
  dir: ./resource/excel/
hua-wei-obs:
  path: you-path
  bucket: you-bucket
  endpoint: you-endpoint
  access-key: you-access-key
  secret-key: you-secret-key
jwt:
  signing-key: gin-vue-admin
  expires-time: 30s
  buffer-time: 30d # 30天缓冲时间，可刷新登录
  issuer: gin-vue-admin
local:
  path: uploads/file
  store-path: uploads/file
minio:
  endpoint: yourEndpoint
  access-key-id: yourAccessKeyId
  access-key-secret: yourAccessKeySecret
  bucket-name: yourBucketName
  use-ssl: false
  base-path: ""
  bucket-url: http://host:9000/yourBucketName
mongo:
  coll: ""
  options: ""
  database: ""
  username: ""
  password: ""
  auth-source: ""
  min-pool-size: 0
  max-pool-size: 100
  socket-timeout-ms: 0
  connect-timeout-ms: 0
  is-zap: false
  hosts:
    - host: ""
      port: ""
mssql:
  prefix: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  path: ""
  engine: ""
  log-mode: ""
  max-idle-conns: 10
  max-open-conns: 100
  singular: false
  log-zap: false
mysql:
  prefix: ""
  port: "3306"
  config: charset=utf8mb4&parseTime=True&loc=Local
  db-name: rapido
  username: root
  password: juezhi@@321
  path: rm-uf6a0w2n3k0d3so837o.mysql.rds.aliyuncs.com
  engine: ""
  log-mode: error
  max-idle-conns: 10
  max-open-conns: 100
  singular: false
  log-zap: false
oracle:
  prefix: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  path: ""
  engine: ""
  log-mode: ""
  max-idle-conns: 10
  max-open-conns: 100
  singular: false
  log-zap: false
pgsql:
  prefix: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  path: ""
  engine: ""
  log-mode: ""
  max-idle-conns: 10
  max-open-conns: 100
  singular: false
  log-zap: false
qiniu:
  zone: ZoneHuaDong
  bucket: ""
  img-path: ""
  access-key: ""
  secret-key: ""
  use-https: false
  use-cdn-domains: false
redis:
  name: cache
  addr: r-uf6k7xu4lzk0mkymg6pd.redis.rds.aliyuncs.com:6379
  password: juezhi@@321
  db: 6
  useCluster: false
  clusterAddrs:
    - **********:7000
    - **********:7001
    - **********:7002
redis-list:
  - name: cache
    addr: r-uf6k7xu4lzk0mkymg6pd.redis.rds.aliyuncs.com:6379
    password: juezhi@@321
    db: 6
    useCluster: false
    clusterAddrs:
      - **********:7000
      - **********:7001
      - **********:7002
  - name: map
    addr: r-uf6k7xu4lzk0mkymg6pd.redis.rds.aliyuncs.com:6379
    password: juezhi@@321
    db: 7
    useCluster: false
    clusterAddrs:
      - **********:7000
      - **********:7001
      - **********:7002
sqlite:
  prefix: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  path: ""
  engine: ""
  log-mode: ""
  max-idle-conns: 10
  max-open-conns: 100
  singular: false
  log-zap: false
system:
  upload-key: guijijike
  domain: https://www.rapido.chat
  default-password: 123456
  db-type: mysql
  oss-type: aliyun-oss
  router-prefix: "/api"
  addr: 8888
  iplimit-count: 60
  iplimit-time: 60
  use-multipoint: true
  use-redis: true
  use-mongo: false
  use-strict-auth: false
  # gRPC相关配置
  grpc-addr: 10000
  enable-grpc: true
  grpc-reflection: true
  master-code: 123456
  master-code-open: true
  # 短信限流配置
  sms-limit-count-ip: 10      # 单个IP每小时最多发送10条短信
  sms-limit-time-ip: 3600     # IP限流时间窗口(秒)
  sms-limit-count-phone: 5    # 单个手机号每小时最多发送5条短信
  sms-limit-time-phone: 3600  # 手机号限流时间窗口(秒)
tencent-cos:
  bucket: xxxxx-********
  region: ap-shanghai
  secret-id: your-secret-id
  secret-key: your-secret-key
  base-url: https://gin.vue.admin
  path-prefix: github.com/flipped-aurora/gin-vue-admin/server
zap:
  level: info
  prefix: '[github.com/flipped-aurora/gin-vue-admin/server]'
  format: console
  director: log
  encode-level: LowercaseColorLevelEncoder
  stacktrace-key: stacktrace
  show-line: true
  log-in-console: true
  retention-day: -1


proxy_server:
  port: 8025

mcp_share_process: true

mcp_servers:
  puppeteer:
    command: "npx -y @modelcontextprotocol/server-puppeteer"
    share_process: false
  # fetch:
  #     command: "uvx mcp-server-fetch"
  #     share_process: true
  time:
    command: "docker run -i --rm mcp/time"
    share_process: false

baidu-translate:
  appid: 20250515002358161
  key: 9yVjE4ik5znRRcgMZx1r

# 支付配置
payment:
  wechat:
    enabled: true  #是否启用
    mp-app-id: wx0c91346de5efe1b7 # 服务号应用ID
    open-app-id: wx27cdbc7d0ba97938 # 网站应用ID
    mch-id: 1717422249 # 商户号
    mch-certificate-serial-number: 35622FE5A509A7540F6C2821FC705C10FFD717F6 #商户API证书序列号
    apiv3-key: guijijike2025fadacai2028shangshi # 微信支付APIv3密钥（32位字符串）

    private-key: |
      -----BEGIN PRIVATE KEY-----
      MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCtWBqIYTjvSNmi
      no/+iF0b0Nmd3O/wUGElJdCXl17sEzyL+FYFBT3KKYo7sSSk+r/k4JvhOUhEYVJi
      5taHrrYM8keelyHIILm9IXvYbSnK/LYoTFXArto9Q63WaD5hrzfeGQjkaZBaTl1J
      kM1DBfzQ3TMkCA/hSDND/HN5mWKEgqW2PQM9lFGeZNH+ww8l9s6dNomlzAjLndyX
      /71CBH2NEcweE9Y93Mj7LTbPQNtXUdToCH4t+bufdFeN1LPawFN4GAmYavC8LN7Z
      D3R28tR1l9FpkFr0sO/+fNfmgpl3oD5Q7RAyNj8vLXM9Hrs/BYTObj8ISXE9EWRT
      VzUYxBhlAgMBAAECggEAXueSt0JLcq63ARLBEYFFQNpqmsSA1Vicp0L53lUw0h2C
      E7wmFF+XQdzWYvIMdwsWDtBuZ7P9/fouqhlqMSUVZScs0QY7p59LMeJoNMknjYwo
      iw2oEXNp5wrr3Sx7ou3Ur5OTS8MEFn07E/DkgKKBlTTWMA796sCl2L4Z1uSEjLk3
      StfOtKWQyYbeQksYDHdIK9EAVZ/TmO60phD0J1LWFxSOnQKRf9JOyDRmKQcwkHO/
      APKj4d4W5wm7sZ5ZGfObnaicWjxIcLX2loMMCKAwgZqzjqtwBAGuM74NFMOZFQg7
      V2ENgIOvqh7kDEgD5ZTPE6EUeC2JZRIfzSnSp+mY4QKBgQDlrofhvVjDk3kCs7+z
      ZOAWr1ozKcWJUa+2LnmsSC9fUQrGpffSTAEr7ohJv1H585/qBFXJWYe0jvJvTrHW
      9Ax/ZmW9UdNu/RkE41bPLv3bTd13Qu2tFqfPjgju+al8QsKgGe6rVS56LDyW67lm
      LGd//LdHyqTaJu+/Z5jv7IZIiQKBgQDBNPgmeHYFRRe/KQrVSFw2hscC2q/3f3NL
      KFKS2pqf+sEumL4DHUYb1/IzBaKgsc+AekdCUk8MPkzvG5HGEz1CjhwTrWQgA/BR
      iLEDxntlqT4ZrAo/p46qUkVtt6sagU34+I4hYvRRsd2IEftx7aW6C2IiMJfxg65T
      udpeMF7h/QKBgQCAGNnx/Y9u7GXWKvfkjoMoQ74TwN1J28Qa4Eq92jiJeYKADfps
      UDVrEgryzoBi3gw3ERnjRUqZWgw+Je6rissZlrtxXhyEzyXQAxbcUEIGuKOmqy4x
      lewbpwS2e4maIwW3Ex94cPaN7vQP3AI3yNYknIZ/xLYycPrPjnDOhuTRuQKBgB6l
      IzMe6kFduy2D+gboak/vwsMuIrkUCxqBs889Q0daw6d7bLAbxZIHB5CWNTxd17be
      Lk1TttwPSa6nZdDQHmhfGA768qWtWkHkd4dMxSfT11jFE/8SuatET2TevBIBLnfu
      HMUo5+3JU4UQrRxeAGppAdDAemlBZLY9BWjpLTOVAoGBAKUGX5ys4u+H7W7DtFRQ
      Od3BDy5dtwyPhNHjqMg2NDU7+rWX4RreqgfBEr4OMKiinTSUZskq8FdwK6bSsvC8
      8UMIs23q7TqKtkHT6MCFWb4sbuy2kh4vyWHj6MdnL3RBUJgtK4qeSFdb+ZAbM1d1
      dW+qN0Gx1pya687qRLtir30f
      -----END PRIVATE KEY-----
    notify-url:  https://rapido.chat/api/payment/notify/wechatNotify # 异步通知地址
    is-sandbox: false # 是否沙箱环境
    # H5支付配置
    h5-domain: rapido.chat # H5支付授权域名（需在微信商户平台配置）
    h5-return-url: https://rapido.chat # H5支付完成后返回地址
  alipay:
    enabled: true # 是否启用 - 改为 true 启用支付宝
    app-id: 2021005156621358 # 应用ID
    private-key: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDgalN6OxPqtquRIRbtj+R3FpaIPMyXo7drPCQK00Z3n165LXetMiUIc9rrnBnwI9Kog+XdUu0/mgermxrCo4jSgrosOh7Ohn5xnMwEL/9RYPcgnt9Utws8OeBsP3v69keyoR9GXZDfxinPaG5IsLTTYVEIjSMHGNBw/QyVug6rUa9alCrp4QH7ePIsMRDEnZXwrMESlbKrQzmnB3uWVAS16OPk3j3lLnFbLPI/YFtyygHJFBi6kgOaKus/pTjXTjcKSYoUCPRCdn6ptwCBBt6S7jgUh8ICRnhiT3T3DV7d+DUy87QK5krZ7Sijhm3mILy9Gb/i+csmun7MCFbqiVcTAgMBAAECggEAL+ijD5ojI12B/QaqZWbtqDrLQiRwrQLHen4pV6JJDeqZB0Fcpj3pSAerudbmdqy/ITifTwOQIJ7aelkvelP9Nquv4Rc7Um3F+q01pMM2Z4Ii3/TqyqQVIvNcJN/csPP41HqvOY9qCUu9y0iJGr2WrPW+neu7iZ1TinuLPz+sOAhe3hgdjDh3pym1Kb+Z+3U2p9jXnyb9PuO9d5cxTK69/oQu4Tq4xXPwYzhhExaanHUJMvHOduYPyQAfQDu2RL9f8xGL1u96yVM/cyFeEpFXZZeM43zqk5Q3jJHlqlMDK1QS/K3wm1C9pLdoSuo8ffnnB0irlVRk7ZjQTJfMPoj1IQKBgQD2DueP6KcKbUURXAeeEfLf4P0aIIvWFKsDK/7UpcCkeH/aR1GH94v3a6CnsC8kof5N6cWpHdFb9Zi1WX8w3IPvV9+w0mtcyb2tg9HyaOxXEeiouwWlmjTqX389IC/01EctNzwmWotpPNIKc0mAowqHgUUxnCvuQqmEO0SVwBxU8QKBgQDpe48d01q4yJoCO/mWNlT3lVu/50AIgSHER63TUDvwLnCRoZ51jfZkNrYPGi+N9yKolUZSnb/RpxkO7+uLEpoJAGzCd5ZZ2We6CAlfMJP9FbU+WJQh/xmHs8dEL6HBYj8yYg5eqOIeY7lfZQ2aIfq5nM/73DQWDWexQiN9bCDcQwKBgEiYzSlgcyhYZPBiPr0cfpQfRY3X3GolXu9oo58J6HSeONqmACAloV42mwfeq4obYXSLbI4hBgwjwhJ/fU9lQGf1fUImUxblTtlK8z6aRmDKH+Loixn5DXO5CUl3na6Wkwl8KnsGa+t4zl/W8/DIKesg8NFaSnvZnzosYmfsbm+BAoGAKTvO/J906ClVOYvJ+kNCIi9Ycr2uS1Iy2m0mP6ltzG8HJlD6oRwfJXecE3K2b0Xz8v3Z24FMfQQbos2wNXFljq1qh7WC8f85GFuO/CaiHBIXXhlan95jbdnMGTioQnTNYGJ80y7UMDXAaR55lnzlwJ33BS3QbQyVAXMCvYW5D+0CgYB3SNyIlkal57O78reT9o0zHCTv84xt9B8ihNl8VSQsoCUJY0NdBIg0P9+3BIg6pZ35bpyI+jVARgoorPvNWKwmrrFlEBc3cfgL7ZWjvZhq9fuKKiEgc9aUYSsafqAaklYNBoBH4Y6VKHnvT+jaOkSa+UNxvsVRPv2XBDM515UlUA== # 应用私钥
    public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAidIUdFvmNi3kpSEXg08mBwfkjQJZJOSrPcXLD6TEFIXRO7NbE+aHz9r8GjJlMiRnNv8M4MkccVb5F0qYpyBS9rZ1L6CvZ/Mmr8vwNgqz78gG5w+s+F82WY7KrzvoITDrWc1n0rk3B07ykVWx5yTPIPGP9V56SFmIRJNCvRv2Rjv1AdoP8ZN0G9tToQnR9O3RdXiIUxVHhhMi/bvktqnGZOfj0/zSHn+O14stSpF1Kv9qDiL5iTK5cCz42j6Sh1zsjqEEIefd0DAtaK0hoUTLnHjRlkaa6L1bhBf5cC64nH50eXHcUcr58cMsPfNWxb22sDgsy8oLlXL97EoHgK0rrQIDAQAB # 支付宝公钥
    is-production: true # 是否生产环境
    notify-url: https://rapido.chat/api/payment/notify/alipayNotify # 异步通知地址
    return-url: https://rapido.chat # 同步返回地址
    sign-type: "RSA2" # 签名类型，默认RSA2

# 企业微信配置
wecom:
  corpid: ww01af6ba28d88ffd2  # 企业ID
  corpsecret: wmrb_weTRiaB-tbINYqSqonB4Sm-J2Y3EvKIY5xHQis  # 应用的凭证密钥
  callbackToken: Giv85UtcWE1WYbToTMKJ # 回调token
  callbackEncodingAESKey: fUeovfp4gPMbURuxzB6F6z429OS6Tfxn7tJtqrEuE7F # 回调加密密钥

# 剪切板配置
clipboard:
  max-file-size: 52428800  # 最大文件大小 50MB
  allowed-file-types: "image,video,audio,document"  # 允许的文件类型
  auto-clean-days: 30  # 自动清理天数
  enable-notification: true  # 是否启用通知

# 阿里云配置
aliyun:
  speech:
    access-key-id: LTAI5tRtpoGymZDsjTHfKQxe
    access-key-secret: ******************************
    app-key: mTFUVnCXwzVputYz
    app-key-cn: XVDNLq994UBxWO0o
  market:
    ip-query:
      app-code: eaa9cae795c340008f86706062c5508c

# 易盾配置
yidun:
  secret-id: d0e4721c83af164a7793dc021928c0a3        # 易盾访问密钥ID（SecretId）
  secret-key: fb037ee2212419f9941383bcdc8a16c3      # 易盾访问密钥Secret（SecretKey）
  captcha-id: a9efe3dc7e974433841a50b5b747ed70      # 易盾验证码ID

# 微信网站应用授权配置
wechat-oauth:
  app-id: wx27cdbc7d0ba97938           # 网站应用AppID
  app-secret: ca51cbe3df2a71ad048acc3d41edab4c  # 网站应用AppSecret
  redirect-uri: "https://www.rapido.chat/api/wechat/callback"  # 授权回调地址
  scope: "snsapi_login"              # 授权作用域：snsapi_base或snsapi_userinfo

# MCP配置
mcp:
  download-script-path: /root/mcp-servers/other/download.sh  # 下载脚本路径

# 腾讯云短信配置
tencent-sms:
  secret-id: AKIDymQr56ydCbn17QccQ1OaZLtFPN8jAQGu        # 腾讯云访问密钥ID
  secret-key: VouolZAanmXItHTBubCbostjOzNbgaHk      # 腾讯云访问密钥Secret
  app-id: 1401014323             # 短信应用ID
  sign-name: 硅基极客  # 短信签名
  template-id: 2480987
  register-template-id: 2480988    # 注册短信模板ID
  login-template-id: 2480987    # 登录短信模板ID
  endpoint: sms.tencentcloudapi.com # 请求端点


ali-sms:
  access-key-id: LTAI5tJQ69dc2DkmHEzimRSR
  access-secret: ******************************
  sign-name: 硅基极客
  template-code: SMS_323755072
  register-template-code: SMS_323755072
  login-template-code: SMS_323755072
# Suno API配置
suno:
    base-url: "https://api.sunoapi.org"    # Suno API基础URL
    api-key: "c06a3e3b06870b2327dc4e97f1f806b7"    